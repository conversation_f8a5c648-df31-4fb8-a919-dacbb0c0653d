import prisma from '../../../config/prismaClient';
import { getUestCoins } from './uestCoinTransactionService';

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
}

interface PurchaseData {
  studentId: string;
  cartItems: CartItem[];
  totalCoins: number;
}

export const processPurchase = async (data: PurchaseData) => {
  const { studentId, cartItems, totalCoins } = data;

  try {
    const result = await prisma.$transaction(async (tx) => {
      const studentCoins = await getUestCoins(studentId, 'STUDENT');
      if (!studentCoins || studentCoins.coins < totalCoins) {
        return {
          success: false,
          error: 'Insufficient coins balance'
        };
      }

      for (const cartItem of cartItems) {
        const storeItem = await tx.storeItem.findUnique({
          where: { id: cartItem.id }
        });

        if (!storeItem) {
          return {
            success: false,
            error: `Store item not found: ${cartItem.name} (ID: ${cartItem.id})`
          };
        }

        if (storeItem.status !== 'ACTIVE') {
          return {
            success: false,
            error: `Item ${cartItem.name} is not available`
          };
        }

        if (storeItem.availableStock < cartItem.quantity) {
          return {
            success: false,
            error: `Insufficient stock for ${cartItem.name}. Available: ${storeItem.availableStock}, Requested: ${cartItem.quantity}`
          };
        }

        if (storeItem.coinPrice !== cartItem.coinPrice) {
          return {
            success: false,
            error: `Price mismatch for ${cartItem.name}`
          };
        }
      }

      const student = await tx.student.findUnique({
        where: { id: studentId }
      });

      if (!student) {
        return {
          success: false,
          error: 'Student not found'
        };
      }

      const orderIds = [];

      for (const cartItem of cartItems) {
        const itemTotalCoins = cartItem.coinPrice * cartItem.quantity;

        const order = await tx.storeOrder.create({
          data: {
            modelId: studentId,
            modelType: 'STUDENT',
            buyerName: `${student.firstName} ${student.lastName}`,
            buyerEmail: student.email,
            itemId: cartItem.id,
            itemName: cartItem.name,
            itemPrice: cartItem.coinPrice,
            quantity: cartItem.quantity,
            totalCoins: itemTotalCoins,
            status: 'COMPLETED'
          }
        });

        orderIds.push(order.id);

        await tx.storeItem.update({
          where: { id: cartItem.id },
          data: {
            availableStock: {
              decrement: cartItem.quantity
            }
          }
        });
      }

      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: studentId,
            modelType: 'STUDENT'
          }
        },
        data: {
          coins: {
            decrement: totalCoins
          }
        }
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: studentId,
          modelType: 'STUDENT',
          amount: totalCoins,
          type: 'DEBIT',
          reason: `Store purchase - ${orderIds.length} items (Order #${orderIds[0].slice(-8)})`
        }
      });

      return { orderIds, firstOrderId: orderIds[0] };
    });

    return {
      success: true,
      orderId: result.firstOrderId,
      orderIds: result.orderIds,
      message: 'Purchase completed successfully'
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Purchase failed'
    };
  }
};

export const getStudentOrders = async (studentId: string) => {
  try {
    const orders = await prisma.storeOrder.findMany({
      where: {
        modelId: studentId,
        modelType: 'STUDENT'
      },
      include: {
        item: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return {
      success: true,
      data: orders
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch orders'
    };
  }
};

export const getOrderById = async (orderId: string, studentId: string) => {
  try {
    const order = await prisma.storeOrder.findFirst({
      where: {
        id: orderId,
        modelId: studentId,
        modelType: 'STUDENT'
      },
      include: {
        item: true
      }
    });

    if (!order) {
      return {
        success: false,
        error: 'Order not found'
      };
    }

    return {
      success: true,
      data: order
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch order'
    };
  }
};
