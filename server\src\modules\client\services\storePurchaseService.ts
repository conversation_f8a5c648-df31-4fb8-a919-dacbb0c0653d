import prisma from '../../../config/prismaClient';
import { getUestCoins } from './uestCoinTransactionService';

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
}

interface PurchaseData {
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  cartItems: CartItem[];
  totalCoins: number;
}

export const processPurchase = async (data: PurchaseData) => {
  const { userId, userType, cartItems, totalCoins } = data;

  try {
    const result = await prisma.$transaction(async (tx) => {
      const userCoins = await getUestCoins(userId, userType);
      if (!userCoins || userCoins.coins < totalCoins) {
        return {
          success: false,
          error: 'Insufficient coins balance'
        };
      }

      for (const cartItem of cartItems) {
        const storeItem = await tx.storeItem.findUnique({
          where: { id: cartItem.id }
        });

        if (!storeItem) {
          return {
            success: false,
            error: `Store item not found: ${cartItem.name} (ID: ${cartItem.id})`
          };
        }

        if (storeItem.status !== 'ACTIVE') {
          return {
            success: false,
            error: `Item ${cartItem.name} is not available`
          };
        }

        if (storeItem.availableStock < cartItem.quantity) {
          return {
            success: false,
            error: `Insufficient stock for ${cartItem.name}. Available: ${storeItem.availableStock}, Requested: ${cartItem.quantity}`
          };
        }

        if (storeItem.coinPrice !== cartItem.coinPrice) {
          return {
            success: false,
            error: `Price mismatch for ${cartItem.name}`
          };
        }
      }

      // Verify user exists based on user type
      if (userType === 'STUDENT') {
        const student = await tx.student.findUnique({
          where: { id: userId },
          select: { id: true }
        });

        if (!student) {
          return {
            success: false,
            error: 'Student not found'
          };
        }
      } else if (userType === 'CLASS') {
        const classUser = await tx.classes.findUnique({
          where: { id: userId },
          select: { id: true }
        });

        if (!classUser) {
          return {
            success: false,
            error: 'Class not found'
          };
        }
      }

      const orderIds = [];

      for (const cartItem of cartItems) {
        const itemTotalCoins = cartItem.coinPrice * cartItem.quantity;

        const order = await tx.storeOrder.create({
          data: {
            modelId: userId,
            modelType: userType,
            itemId: cartItem.id,
            itemName: cartItem.name,
            itemPrice: cartItem.coinPrice,
            quantity: cartItem.quantity,
            totalCoins: itemTotalCoins,
            status: 'COMPLETED'
          }
        });

        orderIds.push(order.id);

        await tx.storeItem.update({
          where: { id: cartItem.id },
          data: {
            availableStock: {
              decrement: cartItem.quantity
            }
          }
        });
      }

      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: userId,
            modelType: userType
          }
        },
        data: {
          coins: {
            decrement: totalCoins
          }
        }
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: userId,
          modelType: userType,
          amount: totalCoins,
          type: 'DEBIT',
          reason: `Store purchase - ${orderIds.length} items (Order #${orderIds[0].slice(-8)})`
        }
      });

      return { orderIds, firstOrderId: orderIds[0] };
    });

    return {
      success: true,
      orderId: result.firstOrderId,
      orderIds: result.orderIds,
      message: 'Purchase completed successfully'
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Purchase failed'
    };
  }
};

export const getUserOrders = async (userId: string, userType: 'STUDENT' | 'CLASS') => {
  try {
    const orders = await prisma.storeOrder.findMany({
      where: {
        modelId: userId,
        modelType: userType
      },
      include: {
        item: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return {
      success: true,
      data: orders
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch orders'
    };
  }
};

export const getOrderById = async (orderId: string, userId: string, userType: 'STUDENT' | 'CLASS') => {
  try {
    const order = await prisma.storeOrder.findFirst({
      where: {
        id: orderId,
        modelId: userId,
        modelType: userType
      },
      include: {
        item: true
      }
    });

    if (!order) {
      return {
        success: false,
        error: 'Order not found'
      };
    }

    return {
      success: true,
      data: order
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to fetch order'
    };
  }
};
