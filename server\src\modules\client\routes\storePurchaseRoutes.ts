import { Router } from 'express';
import { purchaseItems, getMyOrders, getOrderDetails } from '../controllers/storePurchaseController';
import { unifiedAuthMiddleware } from '@/middlewares/unifiedAuth';

const router = Router();

router.post('/purchase', unifiedAuthMiddleware, purchaseItems);

router.get('/orders', unifiedAuthMiddleware, getMyOrders);

router.get('/orders/:orderId', unifiedAuthMiddleware, getOrderDetails);

export default router;
