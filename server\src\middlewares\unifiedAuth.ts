import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { sendError } from '@/utils/response';
import prisma from '@/config/prismaClient';

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';

interface AuthenticatedUser {
  id: string;
  userType: 'STUDENT' | 'CLASS';
  contactNo?: string;
  isVerified?: boolean;
}

declare global {
  namespace Express {
    interface Request {
      authenticatedUser?: AuthenticatedUser;
    }
  }
}

export const unifiedAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const userType = req.headers['user-type'] as string;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload & { 
      id: string; 
      contactNo?: string; 
      isVerified?: boolean;
    };

    if (!decoded || !decoded.id) {
      sendError(res, 'Invalid token', 401);
      return;
    }

    // Determine user type and validate user exists
    let authenticatedUser: AuthenticatedUser;
    
    if (userType === 'STUDENT') {
      // Validate student exists
      const student = await prisma.studentProfile.findUnique({
        where: { id: decoded.id }
      });
      
      if (!student) {
        sendError(res, 'Student not found', 404);
        return;
      }
      
      authenticatedUser = {
        id: decoded.id,
        userType: 'STUDENT',
        contactNo: decoded.contactNo,
        isVerified: decoded.isVerified
      };
    } else if (userType === 'CLASS') {
      // Validate class exists
      const classUser = await prisma.classes.findUnique({
        where: { id: decoded.id }
      });
      
      if (!classUser) {
        sendError(res, 'Class not found', 404);
        return;
      }
      
      authenticatedUser = {
        id: decoded.id,
        userType: 'CLASS',
        contactNo: decoded.contactNo,
        isVerified: decoded.isVerified
      };
    } else {
      sendError(res, 'Invalid user type', 400);
      return;
    }

    req.authenticatedUser = authenticatedUser;
    next();
  } catch (error) {
    console.error('Unified auth middleware error:', error);
    sendError(res, 'Authentication failed', 401);
  }
};
