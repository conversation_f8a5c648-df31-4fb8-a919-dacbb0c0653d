import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const truncateThought = (text: string, wordLimit: number = 5): string => {
  const words = text.trim().split(/\s+/);
  if (words.length <= wordLimit) return text;
  return words.slice(0, wordLimit).join(' ') + '...';
};

export const setStudentAuthToken = (token: string) => {
  localStorage.setItem('studentToken', token);
};

export const getStudentAuthToken = (): string | null => {
  return localStorage.getItem('studentToken');
};

export const clearStudentAuthToken = () => {
  localStorage.removeItem('studentToken');
};

export const isStudentAuthenticated = (): boolean => {
  return !!getStudentAuthToken();
};

// Class authentication utilities
export const setClassAuthToken = (token: string) => {
  localStorage.setItem('classToken', token);
};

export const getClassAuthToken = (): string | null => {
  // Classes store their data in 'user' localStorage with token field
  const userData = localStorage.getItem('user');
  if (userData) {
    try {
      const user = JSON.parse(userData);
      if (user.role === 'classes' && user.token) {
        return user.token;
      }
    } catch (error) {
      console.error('Error parsing user data:', error);
    }
  }
  return localStorage.getItem('classToken');
};

export const clearClassAuthToken = () => {
  localStorage.removeItem('classToken');
  // Also clear user data if it's a class user
  const userData = localStorage.getItem('user');
  if (userData) {
    try {
      const user = JSON.parse(userData);
      if (user.role === 'classes') {
        localStorage.removeItem('user');
      }
    } catch (error) {
      console.error('Error parsing user data:', error);
    }
  }
};

export const isClassAuthenticated = (): boolean => {
  return !!getClassAuthToken();
};

// Combined authentication check
export const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null; token: string | null } => {
  const studentToken = getStudentAuthToken();
  const classToken = getClassAuthToken();

  if (studentToken) {
    return { isAuth: true, userType: 'STUDENT', token: studentToken };
  }

  if (classToken) {
    return { isAuth: true, userType: 'CLASS', token: classToken };
  }

  return { isAuth: false, userType: null, token: null };
};