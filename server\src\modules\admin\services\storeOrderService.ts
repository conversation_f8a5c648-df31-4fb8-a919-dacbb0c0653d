import prisma from '@/config/prismaClient';

export const getAllStoreOrders = async (filters?: {
  status?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}) => {
  try {
    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { modelId: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    const orders = await prisma.storeOrder.findMany({
      where,
      include: {
        item: {
          select: {
            id: true,
            name: true,
            description: true,
            coinPrice: true,
            totalStock: true,
            availableStock: true,
            category: true,
            image: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Fetch buyer information dynamically
    const ordersWithBuyerInfo = await Promise.all(
      orders.map(async (order) => {
        let buyerName = 'Unknown';
        let buyerEmail = null;

        if (order.modelType === 'STUDENT') {
          const student = await prisma.student.findUnique({
            where: { id: order.modelId },
            select: { firstName: true, lastName: true, email: true }
          });
          if (student) {
            buyerName = `${student.firstName} ${student.lastName}`;
            buyerEmail = student.email;
          }
        } else if (order.modelType === 'CLASS') {
          const classUser = await prisma.classes.findUnique({
            where: { id: order.modelId },
            select: { firstName: true, lastName: true, email: true }
          });
          if (classUser) {
            buyerName = `${classUser.firstName} ${classUser.lastName}`;
            buyerEmail = classUser.email;
          }
        }

        return {
          ...order,
          buyerName,
          buyerEmail
        };
      })
    );

    return ordersWithBuyerInfo;

  } catch (error: any) {
    console.error('Error fetching store orders:', error.message);
    throw new Error(`Failed to fetch store orders: ${error.message}`);
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const order = await prisma.storeOrder.findUnique({
      where: { id: orderId },
      include: {
        item: {
          select: {
            id: true,
            name: true,
            description: true,
            coinPrice: true,
            totalStock: true,
            availableStock: true,
            category: true,
            image: true,
            status: true
          }
        }
      }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error: any) {
    console.error('Error fetching order details:', error.message);
    throw new Error(`Failed to fetch order details: ${error.message}`);
  }
};

export const getStoreOrderStats = async () => {
  try {
    const [
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue,
      todayOrders,
      thisMonthOrders
    ] = await Promise.all([
      prisma.storeOrder.count(),
      prisma.storeOrder.count({ where: { status: 'COMPLETED' } }),
      prisma.storeOrder.count({ where: { status: 'PENDING' } }),
      prisma.storeOrder.count({ where: { status: 'CANCELLED' } }),
      prisma.storeOrder.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { totalCoins: true }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ]);

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue: totalRevenue._sum.totalCoins || 0,
      todayOrders,
      thisMonthOrders
    };
  } catch (error: any) {
    console.error('Error fetching order statistics:', error.message);
  }
};

export const updateOrderStatus = async (orderId: string, status: string) => {
  try {
    const validStatuses = ['PENDING', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid order status');
    }

    const order = await prisma.storeOrder.update({
      where: { id: orderId },
      data: { status: status as any }
    });

    return order;
  } catch (error: any) {
    console.error('Error updating order status:', error.message);
    throw new Error(`Failed to update order status: ${error.message}`);
  }
};
